#!/usr/bin/env python3
"""
简单测试脚本
"""

print("开始测试...")

# 测试时间跨度文本生成
ranking_window_days = 7
time_span_text = f"（最近{ranking_window_days}日）" if ranking_window_days else ""

print(f"时间跨度文本: '{time_span_text}'")
print(f"单日冠军标题: '单日冠军次数排行榜{time_span_text}'")
print(f"前10频次标题: '前10名频次排行榜{time_span_text}'")

# 测试不同窗口
for window in [7, 14, 30]:
    time_span = f"（最近{window}日）" if window else ""
    print(f"窗口{window}日: '单日冠军次数排行榜{time_span}'")

print("测试完成！")
